<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="基本信息" name="baseInfo">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        inline
        :disabled="isPreview"
        label-width="80"
        scroll-to-error
      >
        <el-form-item label="名称" prop="Name">
          <el-input v-model="formData.Name" placeholder="请输入名称" @blur="handleBlurName" />
        </el-form-item>
        <el-form-item label="别名" prop="AliasName">
          <el-input v-model="formData.AliasName" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="编码" prop="Code">
          <el-input v-model="formData.Code" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="拼音码" prop="PinyinCode">
          <el-input v-model="formData.PinyinCode" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="是否启用" prop="Enable">
          <el-switch v-model="formData.Enable" />
        </el-form-item>
        <el-form-item label="类别" prop="Type">
          <el-select v-model="formData.Type" placeholder="类别">
            <el-option
              v-for="item in typeList"
              :key="item.Value"
              :label="item.Name"
              :value="item.Value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="频率" prop="Freq">
          <span>一次</span>
          <el-input-number
            v-model="formData.Freq"
            style="width: 100px; margin: 0 10px"
            :min="0"
            :max="100000"
            :step="1"
          />
          <span>次</span>
        </el-form-item>
        <el-form-item label="运动时间" prop="ActionTime">
          <el-input v-model="formData.ActionTime" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item label="运动强度" prop="ActionStrength">
          <el-input v-model="formData.ActionStrength" type="text" style="width: 150px" />
        </el-form-item>
        <el-form-item class="margin-bottom-6" label="厂商" prop="Manufacturer">
          <el-select v-model="formData.Manufacturer" placeholder="请选择厂商" clearable>
            <el-option label="易脑复苏VR" :value="1" />
            <el-option label="赛客呼吸康复" :value="2" />
            <el-option label="脑动极光" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="formData.Manufacturer === 2" label="厂商动作" prop="MFType">
          <el-select v-model="formData.MFType" placeholder="请选择厂商动作" clearable>
            <el-option label="呼气训练" :value="1" />
            <el-option label="吸气训练" :value="2" />
            <el-option label="气道廓清" :value="3" />
          </el-select>
        </el-form-item>
        <template v-if="formData.Type === 0">
          <br />
          <el-form-item prop="GroupCount" label="组数">
            <el-input-number
              v-model="formData.GroupCount"
              style="width: 90px"
              :min="0"
              :max="100000"
              :step="1"
            />
          </el-form-item>
          <el-form-item prop="EachGroupCount" label="每组次数">
            <el-input-number v-model="formData.EachGroupCount" style="width: 90px" />
          </el-form-item>
          <el-form-item class="margin-bottom-6" label="时长">
            <el-input-number
              v-model="formData.Duration"
              style="margin-right: 6px; width: 100px"
              :min="0"
              :max="100000"
              :step="1"
            />
            <el-select v-model="formData.DurationUnit" placeholder="单位" style="width: 80px">
              <el-option
                v-for="item in durationUnitData"
                :key="item.Value"
                :label="item.Name"
                :value="item.Value"
              />
            </el-select>
            <span
              style="
                display: inline-block;
                color: rgb(204, 204, 204);
                padding: 0 6px;
                font-size: 12px;
              "
            >
              （ 每次坚持时间 ）
            </span>
          </el-form-item>
        </template>
        <el-form-item label="部位" prop="Part">
          <el-select v-model="formData.Part" multiple placeholder="请选择">
            <el-option
              v-for="item in partList"
              :key="item.Id"
              :label="item.Key"
              :value="item.Id!"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="Instrument">
          <el-select v-model="formData.Instrument" placeholder="设备类型" clearable>
            <el-option
              v-for="item in instrumentList"
              :key="item.InstrumentId"
              :label="item.Name"
              :value="item.InstrumentId"
            />
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label="功能障碍" prop="Dysfunction" required>
          <TagsSelect
            v-model="formData.Dysfunction"
            :options="dysfunctionList"
            :props="{ label: 'Key', value: 'Id' }"
            :disabled="isPreview"
            :is-back-only-key="true"
          />
        </el-form-item>
        <br />
        <el-form-item label="适用疾病" prop="DiseaseDicts">
          <TagsSelect
            v-model="formData.DiseaseDicts"
            :options="diseaseList"
            :props="{ label: 'Key', value: 'Id' }"
            :disabled="isPreview"
            :is-back-only-key="true"
          />
        </el-form-item>
        <br />
        <el-form-item label="封面图" prop="ActionUnitImgURL">
          <SingleImageUpload v-model="formData.ActionUnitImgURL" />
        </el-form-item>
        <el-form-item label="跟练视频" prop="FollowVideo">
          <FileUpload
            v-model="formData.FollowVideo"
            multiple
            :limit="1"
            accept=".mp4,.mov,.avi,.wmv,.flv,.mkv"
          />
        </el-form-item>
        <br />
        <el-form-item label="视频图片" prop="MediaType">
          <div>
            <el-radio-group v-model="formData.MediaType" @change="fileList = []">
              <el-radio :value="0">视频</el-radio>
              <el-radio :value="1">图片</el-radio>
            </el-radio-group>
            <FileUpload
              v-if="formData.MediaType === 0"
              v-model="fileList"
              accept=".mp4,.mov,.avi,.wmv,.flv,.mkv"
              :limit="1"
            />
            <MultiImageUpload v-else v-model="fileList" :limit="6" />
          </div>
        </el-form-item>
        <br />
        <el-form-item label="动作说明" prop="ActionInfo">
          <el-input v-model="formData.ActionInfo" type="textarea" style="width: 600px" />
        </el-form-item>
        <br />
        <el-form-item label="注意事项">
          <el-input v-model="formData.Notes" type="textarea" style="width: 600px" />
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { ProcessActionUnitInputDTO } from "@/api/content/types";
import { chineseToPinyin } from "@/utils";
import { getVideoFirstFrame } from "@/utils/utils";
import { FormInstance, FormRules } from "element-plus";

const formData = ref<any>({
  Name: "",
  Code: "",
  PinyinCode: "",
  Type: 0,
  Freq: null,
  GroupCount: null,
  EachGroupCount: null,
  Duration: null,
  Enable: true,
  DurationUnit: null,
  Dysfunction: [],
  FollowVideo: [],
  DiseaseDicts: [],
  Part: [],
  Instrument: "",
  MediaType: 0,
  Media: [],
  ActionUnitImgURL: "",
  ActionInfo: "请参考视频训练",
  Notes:
    "量力而行，循序渐进；训练过程中有剧烈疼痛不适，可减轻活动范围或停止训练，并及时联系医生或治疗师。",

  IntensiveTrainings: [], // 这个是对应的 第二个的el-tab-pane 目前现在不需要这个了
  UseScope: 0,
  MFType: null,
  ActionTime: "",
  ActionStrength: "",
  AliasName: "",
  Manufacturer: null,
});
const formRef = ref<FormInstance>();
const activeName = ref<string>("baseInfo");
const isPreview = inject("isPreview") as boolean;
const partList = inject("partList") as ReadDict[];
const instrumentList = inject("instrumentList") as BaseInstrument[];
const dysfunctionList = inject("dysfunctionList") as ReadDict[];
const diseaseList = inject("diseaseList") as ReadDict[];
const typeList = ref<{ Name: string; Value: number }[]>([
  // 类别选择
  {
    Name: "治疗性运动训练",
    Value: 0,
  },
  {
    Name: "日常生活活动能力训练",
    Value: 1,
  },
]);
const durationUnitData = ref<{ Name: string; Value: number }[]>([
  // 时长单位
  {
    Name: "分钟",
    Value: 0,
  },
  {
    Name: "秒",
    Value: 1,
  },
]);
const fileList = ref<string[]>([]);

const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Code: [
    {
      required: true,
      message: "请输入编码",
      trigger: "blur",
    },
  ],
  PinyinCode: [
    {
      required: true,
      message: "请输入拼音码",
      trigger: "blur",
    },
  ],
  Type: [
    {
      required: true,
      message: "请选择类别",
      trigger: "change",
    },
  ],
  Freq: [
    {
      required: true,
      message: "请输入频率次数",
      trigger: "blur",
    },
  ],
  Duration: [
    {
      message: "时长为正整数",
      trigger: "blur",
    },
  ],
  MediaType: [
    {
      required: true,
      message: "请上传图片",
      trigger: "change",
    },
  ],
  ActionInfo: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请输入动作说明",
      trigger: "blur",
    },
  ],
  Dysfunction: [{ required: true, message: "请选择功能障碍", trigger: "change" }],
});

const handleGetContentInfo = async (contentId: string) => {
  const res = await Content_Api.queryActionUnitById({ id: contentId });
  if (res.Type === 200) {
    console.log(res.Data);
    formData.value.Name = res.Data.Name;
    formData.value.Code = res.Data.Code;
    formData.value.PinyinCode = res.Data.PinyinCode;
    formData.value.Type = res.Data.Type;
    formData.value.Freq = res.Data.Freq;
    formData.value.GroupCount = res.Data.GroupCount;
    formData.value.EachGroupCount = res.Data.EachGroupCount;
    formData.value.Duration = res.Data.Duration;
    formData.value.Enable = res.Data.Enable;
    formData.value.DurationUnit = res.Data.DurationUnit;
    formData.value.Dysfunction = res.Data.Dysfunction;
    formData.value.FollowVideo = res.Data.FollowVideo;
    formData.value.DiseaseDicts = res.Data.DiseaseDicts;
    formData.value.Part = res.Data.Part;
    formData.value.Instrument = res.Data.Instrument;
    formData.value.MediaType = res.Data.MediaType;
    formData.value.Media = res.Data.Media;
    formData.value.ActionUnitImgURL = res.Data.ActionUnitImgURL;
    formData.value.ActionInfo = res.Data.ActionInfo;
    formData.value.Notes = res.Data.Notes;
    formData.value.IntensiveTrainings = res.Data.IntensiveTrainings;
    formData.value.UseScope = res.Data.UseScope;
    formData.value.MFType = res.Data.MFType;
    formData.value.ActionTime = res.Data.ActionTime;
    formData.value.ActionStrength = res.Data.ActionStrength;
    formData.value.AliasName = res.Data.AliasName;
    formData.value.Manufacturer = res.Data.Manufacturer;
    if (res.Data.Media && res.Data.Media.length) {
      fileList.value = res.Data.Media.map((item) => item.Url);
    }
  }
};

const handleBlurName = () => {
  formData.value.PinyinCode = chineseToPinyin(formData.value.Name);
};

const handleSubmit = async (): Promise<ProcessActionUnitInputDTO | null> => {
  try {
    await formRef.value?.validate();
    // 将页面的数据处理为借口需要的格式返回
    const serverData = processFormDataToServerData();
    console.log(serverData);
    return serverData;
  } catch (error) {
    return null;
  }
};

const processFormDataToServerData = (): ProcessActionUnitInputDTO => {
  const params = {
    ...formData.value,
  };
  if (fileList.value && fileList.value.length) {
    params.Media = fileList.value.map((item) => ({ url: item }));
  }
  if (props.contentId) {
    params.Id = props.contentId;
  }
  return params;
};

interface Props {
  contentId: string;
}
const props = defineProps<Props>();
watch(
  () => props.contentId,
  (newVal) => {
    if (newVal) {
      handleGetContentInfo(newVal);
    }
  },
  { immediate: true }
);
watch(
  () => fileList.value,
  async (newVal) => {
    if (newVal && newVal.length && formData.value.MediaType === 0 && !isPreview) {
      const imageUrl = await getVideoFirstFrame(newVal[0]);
      formData.value.ActionUnitImgURL = imageUrl;
    }
  }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
